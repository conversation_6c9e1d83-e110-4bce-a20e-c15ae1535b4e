# 🔧 Guide de Configuration Email - Constat Tunisie

## 🎯 Problème Actuel
Les emails d'invitation ne sont pas envoyés car les services d'email ne sont pas correctement configurés.

## ✅ Solution Rapide (5 minutes)

### **Option 1: EmailJS (Recommandé)**

1. **C<PERSON>er un compte EmailJS**
   - Allez sur [emailjs.com](https://www.emailjs.com)
   - Inscrivez-vous avec votre email Gmail

2. **Configurer le service Gmail**
   - Dans EmailJS, cliquez "Add New Service"
   - Choisissez "Gmail"
   - Connectez votre compte Gmail
   - **Copiez le Service ID** (ex: `service_abc123`)

3. **<PERSON><PERSON>er un template d'email**
   - Cliquez "Email Templates" → "Create New Template"
   - **Subject:** `{{subject}}`
   - **Content:**
   ```
   {{message}}
   
   Code de session: {{session_code}}
   Lien d'invitation: {{invitation_link}}
   ```
   - **Co<PERSON><PERSON> le Template ID** (ex: `template_xyz789`)

4. **Obtenir la clé publique**
   - Allez dans "Account" > "General"
   - **Copiez votre Public Key** (ex: `user_def456`)

5. **Mettre à jour le code**
   - Ouvrez `lib/core/services/email_service.dart`
   - Remplacez les valeurs:
   ```dart
   static const String _emailjsServiceId = 'VOTRE_SERVICE_ID';
   static const String _emailjsTemplateId = 'VOTRE_TEMPLATE_ID';
   static const String _emailjsPublicKey = 'VOTRE_PUBLIC_KEY';
   ```

### **Option 2: Formspree (Alternative)**

1. **Créer un compte Formspree**
   - Allez sur [formspree.io](https://formspree.io)
   - Créez un compte gratuit

2. **Créer un nouveau formulaire**
   - Cliquez "New Form"
   - Utilisez votre email comme destinataire

3. **Copier l'URL du formulaire**
   - Copiez l'URL (ex: `https://formspree.io/f/xpwzgqpv`)

4. **Mettre à jour le code**
   ```dart
   static const String _formspreeUrl = 'VOTRE_URL_FORMSPREE';
   ```

## 🧪 Test de Configuration

1. **Lancer l'application**
2. **Créer une session collaborative**
3. **Entrer votre propre email comme destinataire**
4. **Vérifier les logs dans la console**
5. **Vérifier votre boîte email (et spam)**

## 📋 Logs à Surveiller

```
[EmailService] 📧 Tentative d'envoi avec EmailJS...
[EmailService] 📊 Réponse EmailJS: 200
[EmailService] ✅ Email envoyé avec succès via EmailJS
```

## ⚠️ Problèmes Courants

### **Erreur 401/403**
- Vérifiez vos clés API
- Assurez-vous que le service Gmail est bien connecté

### **Erreur 400**
- Vérifiez le format du template
- Assurez-vous que tous les paramètres sont présents

### **Email non reçu**
- Vérifiez le dossier spam
- Attendez quelques minutes (délai de livraison)
- Testez avec un autre email

## 🎯 Email de Test

Pour tester rapidement, utilisez cette adresse email de test:
`<EMAIL>`

## 📞 Support

Si vous avez des problèmes:
1. Vérifiez les logs de l'application
2. Testez avec votre propre email d'abord
3. Assurez-vous que votre connexion internet fonctionne
