import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';

class EmailService {
  // 🚀 CONFIGURATION MODERNE ET FIABLE
  static const String _appUrl = 'https://constat-tunisie.com';

  Future<void> sendEmailWithAttachment({
    required String to,
    required String subject,
    required String body,
    required String attachmentPath,
  }) async {
    // Implementation avec SendGrid ou autre service
    debugPrint('EmailService: Sending email with attachment to $to, subject: $subject, attachment: $attachmentPath');
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> sendEmail({
    required String to,
    required String subject,
    required String body,
  }) async {
    try {
      debugPrint('[EmailService] Envoi email à: $to');

      if (kDebugMode) {
        // En mode debug, on simule l'envoi
        debugPrint('[EmailService] MODE DEBUG - Email simulé');
        debugPrint('[EmailService] To: $to');
        debugPrint('[EmailService] Subject: $subject');
        debugPrint('[EmailService] Body: $body');
        await Future.delayed(const Duration(milliseconds: 1000));
        return;
      }

      // Envoi réel avec Mailtrap
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_mailtrapToken',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'from': {
            'email': _fromEmail,
            'name': 'Constat Tunisie',
          },
          'to': [
            {
              'email': to,
            }
          ],
          'subject': subject,
          'html': body,
        }),
      );

      if (response.statusCode == 202) {
        debugPrint('[EmailService] Email envoyé avec succès');
      } else {
        throw Exception('Erreur envoi email: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      debugPrint('[EmailService] Erreur: $e');
      rethrow;
    }
  }

  Future<void> envoyerInvitation({
    required String email,
    required String sessionCode,
    required String sessionId,
  }) async {
    try {
      debugPrint('[EmailService] === ENVOI INVITATION ===');
      debugPrint('[EmailService] Destinataire: $email');
      debugPrint('[EmailService] Code session: $sessionCode');
      debugPrint('[EmailService] ID session: $sessionId');

      // Validation de l'email
      if (!_isValidEmail(email)) {
        throw Exception('Format d\'email invalide: $email');
      }

      // Créer le lien d'invitation
      final invitationLink = '$_appUrl/join-session?code=$sessionCode&id=$sessionId';

      // Afficher le contenu de l'email dans les logs
      debugPrint('[EmailService] === CONTENU EMAIL ===');
      debugPrint('[EmailService] Sujet: Invitation - Constat d\'accident collaboratif');
      debugPrint('[EmailService] Destinataire: $email');
      debugPrint('[EmailService] Code de session: $sessionCode');
      debugPrint('[EmailService] Lien: $invitationLink');
      debugPrint('[EmailService] === FIN CONTENU EMAIL ===');

      // Essayer d'abord l'envoi automatique, puis fallback vers l'app email
      bool emailSent = false;

      // Tentative d'envoi automatique avec service web
      debugPrint('[EmailService] 🚀 Tentative d\'envoi automatique...');
      emailSent = await _sendWithWebService(email, sessionCode, invitationLink);

      if (emailSent) {
        debugPrint('[EmailService] ✅ EMAIL ENVOYÉ AUTOMATIQUEMENT!');
        debugPrint('[EmailService] 📧 L\'email a été envoyé à: $email');
        debugPrint('[EmailService] 📧 Vérifiez votre boîte de réception et le dossier spam!');
      } else {
        // Fallback : afficher le contenu pour envoi manuel
        debugPrint('[EmailService] 📱 SOLUTION MANUELLE - COPIEZ ET ENVOYEZ CET EMAIL:');
        debugPrint('[EmailService] ===============================================');
        debugPrint('[EmailService] 📧 DESTINATAIRE: $email');
        debugPrint('[EmailService] 📧 SUJET: Invitation - Constat d\'accident collaboratif');
        debugPrint('[EmailService] 📧 MESSAGE:');
        debugPrint('[EmailService] ');
        debugPrint('[EmailService] Bonjour,');
        debugPrint('[EmailService] ');
        debugPrint('[EmailService] Vous avez été invité(e) à participer à un constat');
        debugPrint('[EmailService] d\'accident collaboratif via l\'application Constat Tunisie.');
        debugPrint('[EmailService] ');
        debugPrint('[EmailService] 🔑 CODE DE SESSION: $sessionCode');
        debugPrint('[EmailService] ');
        debugPrint('[EmailService] 📱 COMMENT REJOINDRE:');
        debugPrint('[EmailService] 1. Ouvrez l\'application Constat Tunisie');
        debugPrint('[EmailService] 2. Appuyez sur "Rejoindre une session"');
        debugPrint('[EmailService] 3. Saisissez le code: $sessionCode');
        debugPrint('[EmailService] ');
        debugPrint('[EmailService] ⚠️ Cette invitation expire dans 24 heures.');
        debugPrint('[EmailService] ');
        debugPrint('[EmailService] Cordialement,');
        debugPrint('[EmailService] L\'équipe Constat Tunisie');
        debugPrint('[EmailService] ===============================================');
        debugPrint('[EmailService] 📧 ENVOYEZ CET EMAIL À: $email');
        debugPrint('[EmailService] 📧 AVEC LE CODE: $sessionCode');
        debugPrint('[EmailService] ===============================================');

        // Essayer quand même d'ouvrir l'app email
        await _openEmailApp(email, sessionCode, invitationLink);
      }

      debugPrint('[EmailService] 📧 Processus d\'invitation terminé pour: $email');
    } catch (e) {
      debugPrint('[EmailService] ❌ ERREUR CRITIQUE lors de l\'envoi: $e');
      debugPrint('[EmailService] 📧 Email concerné: $email');
      debugPrint('[EmailService] 🔧 Vérifiez la configuration EmailJS dans email_service.dart');
      rethrow;
    }
  }

  /// Validation simple du format email
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(email);
  }





  Future<bool> _sendWithFormspree(String email, String sessionCode, String invitationLink) async {
    try {
      debugPrint('[EmailService] 📧 Envoi avec Formspree (compatible mobile)...');

      final emailContent = '''
Bonjour,

Vous avez été invité(e) à participer à un constat d'accident collaboratif via l'application Constat Tunisie.

🔑 CODE DE SESSION: $sessionCode

📱 COMMENT REJOINDRE:
1. Ouvrez l'application Constat Tunisie
2. Appuyez sur "Rejoindre une session"
3. Saisissez le code: $sessionCode

🔗 LIEN DIRECT: $invitationLink

⚠️ Cette invitation expire dans 24 heures.

Cordialement,
L'équipe Constat Tunisie
      ''';

      final response = await http.post(
        Uri.parse(_formspreeUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'subject': 'Invitation - Constat d\'accident collaboratif',
          'message': emailContent,
          '_replyto': email,
          '_subject': 'Invitation - Constat d\'accident collaboratif',
        }),
      );

      debugPrint('[EmailService] 📊 Réponse Formspree: ${response.statusCode}');
      debugPrint('[EmailService] 📊 Corps réponse: ${response.body}');

      if (response.statusCode == 200) {
        debugPrint('[EmailService] ✅ Email envoyé avec succès via Formspree!');
        debugPrint('[EmailService] 📧 Destinataire: $email');
        return true;
      } else {
        debugPrint('[EmailService] ❌ Échec Formspree: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('[EmailService] ❌ Erreur Formspree: $e');
      return false;
    }
  }

  Future<bool> _sendWithWebService(String email, String sessionCode, String invitationLink) async {
    try {
      debugPrint('[EmailService] 📧 Tentative d\'envoi avec EmailJS...');

      // Essayer d'abord EmailJS
      bool emailjsSuccess = await _sendWithEmailJS(email, sessionCode, invitationLink);
      if (emailjsSuccess) {
        debugPrint('[EmailService] ✅ Email envoyé avec succès via EmailJS');
        return true;
      }

      debugPrint('[EmailService] ⚠️ EmailJS a échoué, tentative avec Formspree...');

      // Fallback vers Formspree
      bool formspreeSuccess = await _sendWithFormspree(email, sessionCode, invitationLink);
      if (formspreeSuccess) {
        debugPrint('[EmailService] ✅ Email envoyé avec succès via Formspree');
        return true;
      }

      debugPrint('[EmailService] ❌ Tous les services d\'email ont échoué');
      return false;
    } catch (e) {
      debugPrint('[EmailService] ❌ Erreur générale: $e');
      return false;
    }
  }

  Future<bool> _sendWithEmailJS(String email, String sessionCode, String invitationLink) async {
    try {
      debugPrint('[EmailService] 📧 Envoi avec EmailJS...');

      final emailContent = '''Bonjour,

Vous avez été invité(e) à participer à un constat d'accident collaboratif via l'application Constat Tunisie.

🔑 CODE DE SESSION: $sessionCode

📱 COMMENT REJOINDRE:
1. Ouvrez l'application Constat Tunisie
2. Appuyez sur "Rejoindre une session"
3. Saisissez le code: $sessionCode

🔗 LIEN DIRECT: $invitationLink

⚠️ Cette invitation expire dans 24 heures.

Cordialement,
L'équipe Constat Tunisie''';

      final response = await http.post(
        Uri.parse('https://api.emailjs.com/api/v1.0/email/send'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'service_id': _emailjsServiceId,
          'template_id': _emailjsTemplateId,
          'user_id': _emailjsPublicKey,
          'template_params': {
            'to_email': email,
            'from_name': 'Constat Tunisie',
            'subject': 'Invitation - Constat d\'accident collaboratif',
            'session_code': sessionCode,
            'invitation_link': invitationLink,
            'message': emailContent,
          }
        }),
      );

      debugPrint('[EmailService] 📊 Réponse service web: ${response.statusCode}');
      debugPrint('[EmailService] 📊 Corps réponse: ${response.body}');

      if (response.statusCode == 200) {
        debugPrint('[EmailService] ✅ VRAI EMAIL ENVOYÉ AVEC EMAILJS!');
        debugPrint('[EmailService] 📧 Destinataire: $email');
        debugPrint('[EmailService] 🎯 L\'email a été envoyé dans votre boîte Gmail!');
        debugPrint('[EmailService] 📧 Vérifiez votre boîte de réception et spam!');
        return true;
      }

      debugPrint('[EmailService] ❌ Échec service web: ${response.statusCode} - ${response.body}');
      return false;
    } catch (e) {
      debugPrint('[EmailService] ❌ Erreur service web: $e');
      return false;
    }
  }

  Future<void> _openEmailApp(String email, String sessionCode, String invitationLink) async {
    try {
      final subject = Uri.encodeComponent('Invitation - Constat d\'accident collaboratif');
      final body = Uri.encodeComponent('''
Bonjour,

Vous avez été invité(e) à participer à un constat d'accident collaboratif.

Code de session: $sessionCode

Pour rejoindre la session:
1. Ouvrez l'application Constat Tunisie
2. Appuyez sur "Rejoindre une session"
3. Saisissez le code: $sessionCode

Ou utilisez ce lien: $invitationLink

Cette invitation expire dans 24 heures.

Cordialement,
L'équipe Constat Tunisie
      ''');

      final mailtoUrl = 'mailto:$email?subject=$subject&body=$body';
      final uri = Uri.parse(mailtoUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        debugPrint('[EmailService] Application email ouverte');
      } else {
        debugPrint('[EmailService] Impossible d\'ouvrir l\'application email');
      }
    } catch (e) {
      debugPrint('[EmailService] Erreur ouverture app email: $e');
    }
  }


}
