# 🔧 Dépannage - Problèmes d'Envoi d'Emails

## 🎯 Symptômes Courants

### ❌ "Les emails ne sont pas envoyés"
### ❌ "Je ne reçois aucun email"
### ❌ "Les conducteurs ne reçoivent pas les invitations"

## 🔍 Diagnostic Rapide

### **Étape 1: Vérifier les Logs**
Ouvrez la console de développement et cherchez ces messages:

✅ **Succès:**
```
[EmailService] ✅ Email envoyé avec succès via EmailJS
[EmailService] 📧 Destinataire: <EMAIL>
```

❌ **Échec:**
```
[EmailService] ❌ Erreur EmailJS: 401 - Unauthorized
[EmailService] ❌ Tous les services d'email ont échoué
```

### **Étape 2: Tester l'Envoi**
1. Ouvrez l'application
2. Allez dans "Créer une session collaborative"
3. Cliquez sur "Tester l'envoi d'email" (nouveau bouton)
4. Vérifiez les logs et votre email

### **Étape 3: Vérifier la Configuration**

#### **Configuration EmailJS:**
Dans `lib/core/services/email_service.dart`, vérifiez:
```dart
static const String _emailjsServiceId = 'service_hcur24e';  // ✅ Correct
static const String _emailjsTemplateId = 'template_71a57ow'; // ✅ Correct  
static const String _emailjsPublicKey = 'IjxWFDFy9vM0bmTjZ'; // ✅ Correct
```

## 🛠️ Solutions par Problème

### **Problème 1: Erreur 401/403 (Non autorisé)**

**Cause:** Clés API incorrectes ou service non configuré

**Solution:**
1. Vérifiez vos clés EmailJS
2. Assurez-vous que le service Gmail est connecté
3. Vérifiez que le template existe

### **Problème 2: Erreur 400 (Mauvaise requête)**

**Cause:** Template mal configuré ou paramètres manquants

**Solution:**
1. Vérifiez votre template EmailJS
2. Assurez-vous qu'il contient: `{{message}}`, `{{session_code}}`, `{{invitation_link}}`

### **Problème 3: Email non reçu (Code 200)**

**Cause:** Email envoyé mais pas livré

**Solution:**
1. ✅ Vérifiez le dossier **SPAM/INDÉSIRABLES**
2. ✅ Attendez 2-5 minutes (délai de livraison)
3. ✅ Testez avec un autre email
4. ✅ Vérifiez que l'email destinataire existe

### **Problème 4: Validation d'email trop stricte**

**Cause:** Le système bloque les emails valides

**Solution:** ✅ **DÉJÀ CORRIGÉ** - La validation accepte maintenant tous les emails avec un format valide

## 🧪 Tests Recommandés

### **Test 1: Email Personnel**
```
Email de test: <EMAIL>
Résultat attendu: Email reçu dans les 2-5 minutes
```

### **Test 2: Email Cible**
```
Email de test: <EMAIL>
Résultat attendu: Email reçu (vérifiez avec l'utilisateur)
```

### **Test 3: Emails Multiples**
```
Testez avec 2-3 emails différents
Vérifiez que tous reçoivent l'invitation
```

## 📋 Checklist de Vérification

- [ ] Configuration EmailJS correcte
- [ ] Service Gmail connecté dans EmailJS
- [ ] Template d'email créé et configuré
- [ ] Clés API mises à jour dans le code
- [ ] Test d'envoi réussi
- [ ] Email reçu (vérifier spam)
- [ ] Logs montrent un succès (code 200)

## 🆘 Si Rien ne Fonctionne

### **Plan B: Configuration Alternative**

1. **Créez un compte Formspree** (gratuit)
2. **Mettez à jour l'URL Formspree** dans le code
3. **Testez avec Formspree**

### **Plan C: Mode Manuel**
Si les services automatiques échouent, l'application ouvrira automatiquement votre application email avec le contenu pré-rempli.

## 📞 Support Technique

**Informations à fournir:**
1. Logs complets de la console
2. Code de réponse HTTP (200, 401, 403, etc.)
3. Email de test utilisé
4. Configuration EmailJS utilisée

**Email de contact:** Consultez la documentation du projet
